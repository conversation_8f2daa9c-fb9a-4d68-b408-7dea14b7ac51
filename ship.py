"""
Ship class for Battleships game.
Represents individual ships with their properties and state.
"""

class Ship:
    """Represents a ship in the Battleships game."""

    def __init__(self, name, size):
        """
        Initialize a ship.

        Args:
            name (str): Name of the ship (e.g., "Carrier", "Battleship")
            size (int): Length of the ship in grid squares
        """
        self.name = name
        self.size = size
        self.positions = []  # List of (row, col) tuples where ship is placed
        self.hits = set()    # Set of (row, col) tuples where ship has been hit
        self.is_placed = False

    def place(self, start_row, start_col, orientation):
        """
        Place the ship on the board.

        Args:
            start_row (int): Starting row (0-9)
            start_col (int): Starting column (0-9)
            orientation (str): 'horizontal' or 'vertical'

        Returns:
            bool: True if placement is valid, False otherwise
        """
        positions = []

        if orientation == 'horizontal':
            if start_col + self.size > 10:  # Would go off board
                return False
            positions = [(start_row, start_col + i) for i in range(self.size)]
        elif orientation == 'vertical':
            if start_row + self.size > 10:  # Would go off board
                return False
            positions = [(start_row + i, start_col) for i in range(self.size)]
        else:
            return False

        self.positions = positions
        self.is_placed = True
        return True

    def hit(self, row, col):
        """
        Register a hit on the ship.

        Args:
            row (int): Row coordinate
            col (int): Column coordinate

        Returns:
            bool: True if this position contains part of the ship
        """
        if (row, col) in self.positions:
            self.hits.add((row, col))
            return True
        return False

    def is_sunk(self):
        """
        Check if the ship is completely sunk.

        Returns:
            bool: True if all positions have been hit
        """
        return len(self.hits) == self.size

    def get_positions(self):
        """
        Get all positions occupied by this ship.

        Returns:
            list: List of (row, col) tuples
        """
        return self.positions.copy()

    def __str__(self):
        """String representation of the ship."""
        status = "sunk" if self.is_sunk() else f"{len(self.hits)}/{self.size} hits"
        return f"{self.name} ({self.size} squares) - {status}"


# Standard Hasbro Battleships fleet
FLEET_COMPOSITION = [
    ("Carrier", 5),
    ("Battleship", 4),
    ("Cruiser", 3),
    ("Submarine", 3),
    ("Destroyer", 2)
]


def create_fleet():
    """
    Create a standard fleet of ships for Battleships.

    Returns:
        list: List of Ship objects representing the standard fleet
    """
    return [Ship(name, size) for name, size in FLEET_COMPOSITION]