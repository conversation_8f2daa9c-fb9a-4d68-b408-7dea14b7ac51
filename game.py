"""
Main Game class for Battleships.
Manages game flow, turns, and user interface.
"""

import os
import sys
from player import Player


class BattleshipsGame:
    """Main game controller for Battleships."""

    def __init__(self):
        """Initialize the game."""
        self.player1 = None
        self.player2 = None
        self.current_player = None
        self.other_player = None
        self.game_over = False
        self.winner = None

    def clear_screen(self):
        """Clear the console screen."""
        os.system('cls' if os.name == 'nt' else 'clear')

    def display_board(self, board_grid, title, show_coordinates=True):
        """
        Display a game board.

        Args:
            board_grid (list): 2D list representing the board
            title (str): Title to display above the board
            show_coordinates (bool): Whether to show row/column labels
        """
        print(f"\n{title}")
        print("=" * len(title))

        if show_coordinates:
            print("   ", end="")
            for col in range(10):
                print(f" {col} ", end="")
            print()

        for row in range(10):
            if show_coordinates:
                print(f"{row:2} ", end="")
            for col in range(10):
                cell = board_grid[row][col]
                print(f" {cell} ", end="")
            print()

    def display_legend(self):
        """Display the legend for board symbols."""
        print("\nLegend:")
        print("~ = Water")
        print("S = Ship")
        print("X = Hit")
        print("O = Miss")

    def get_player_input(self, prompt, valid_inputs=None):
        """
        Get input from player with validation.

        Args:
            prompt (str): Input prompt
            valid_inputs (list): List of valid inputs, or None for any input

        Returns:
            str: Valid user input
        """
        while True:
            user_input = input(prompt).strip()
            if valid_inputs is None or user_input.lower() in [v.lower() for v in valid_inputs]:
                return user_input
            print(f"Invalid input. Please enter one of: {', '.join(valid_inputs)}")

    def get_coordinate_input(self, prompt):
        """
        Get coordinate input from player.

        Args:
            prompt (str): Input prompt

        Returns:
            tuple: (row, col) coordinates, or None if invalid
        """
        while True:
            try:
                coord_input = input(prompt).strip().upper()
                if len(coord_input) < 2:
                    raise ValueError("Too short")

                # Parse input like "A5" or "B10"
                row_char = coord_input[0]
                col_str = coord_input[1:]

                if row_char < 'A' or row_char > 'J':
                    raise ValueError("Row must be A-J")

                row = ord(row_char) - ord('A')
                col = int(col_str)

                if col < 0 or col > 9:
                    raise ValueError("Column must be 0-9")

                return row, col

            except (ValueError, IndexError):
                print("Invalid coordinate. Please enter like 'A5' (row A-J, column 0-9)")

    def setup_players(self):
        """Set up the two players."""
        print("Welcome to Battleships!")
        print("=" * 20)

        name1 = input("Enter name for Player 1: ").strip() or "Player 1"
        name2 = input("Enter name for Player 2: ").strip() or "Player 2"

        self.player1 = Player(name1)
        self.player2 = Player(name2)

        print(f"\n{name1} vs {name2}")
        print("Let's place your ships!")

    def ship_placement_phase(self):
        """Handle ship placement for both players."""
        for player in [self.player1, self.player2]:
            self.place_ships_for_player(player)

    def place_ships_for_player(self, player):
        """
        Handle ship placement for a single player.

        Args:
            player (Player): Player placing ships
        """
        self.clear_screen()
        print(f"\n{player.name}'s turn to place ships")
        print("=" * 30)

        unplaced_ships = player.get_unplaced_ships()

        while unplaced_ships:
            # Display current board
            self.display_board(player.get_board_display(), f"{player.name}'s Board")
            self.display_legend()

            # Show remaining ships
            print(f"\nShips to place:")
            for i, ship in enumerate(unplaced_ships):
                print(f"{i}: {ship.name} (size {ship.size})")

            # Get ship selection
            try:
                ship_choice = int(input(f"\nSelect ship to place (0-{len(unplaced_ships)-1}): "))
                if ship_choice < 0 or ship_choice >= len(unplaced_ships):
                    print("Invalid ship selection!")
                    input("Press Enter to continue...")
                    continue

                ship = unplaced_ships[ship_choice]

                # Get placement coordinates
                print(f"\nPlacing {ship.name} (size {ship.size})")
                row, col = self.get_coordinate_input("Enter starting position (e.g., A5): ")

                orientation = self.get_player_input(
                    "Enter orientation (H for horizontal, V for vertical): ",
                    ['H', 'V']
                )
                orientation = 'horizontal' if orientation.upper() == 'H' else 'vertical'

                # Try to place the ship
                ship_index = player.fleet.index(ship)
                if player.place_ship(ship_index, row, col, orientation):
                    print(f"{ship.name} placed successfully!")
                    unplaced_ships = player.get_unplaced_ships()
                else:
                    print("Cannot place ship there! Check for overlaps or boundaries.")

                input("Press Enter to continue...")
                self.clear_screen()

            except (ValueError, IndexError):
                print("Invalid input!")
                input("Press Enter to continue...")

        print(f"\n{player.name} has placed all ships!")
        input("Press Enter to continue...")
        self.clear_screen()

    def battle_phase(self):
        """Handle the main battle phase of the game."""
        self.current_player = self.player1
        self.other_player = self.player2

        print("Battle begins!")
        input("Press Enter to start the battle...")

        while not self.game_over:
            self.play_turn()

            # Check for winner
            if self.other_player.has_lost():
                self.game_over = True
                self.winner = self.current_player
                break

            # Switch players
            self.current_player, self.other_player = self.other_player, self.current_player

        self.display_game_over()

    def play_turn(self):
        """Handle a single player's turn."""
        self.clear_screen()

        print(f"\n{self.current_player.name}'s Turn")
        print("=" * 20)

        # Show both boards
        print("\nYour Board:")
        self.display_board(self.current_player.get_board_display(), "Your Ships")

        print(f"\n{self.other_player.name}'s Board (Your Target):")
        self.display_board(self.other_player.get_board_display(show_ships=False), "Enemy Waters")

        self.display_legend()

        # Show fleet status
        print(f"\nYour Fleet Status:")
        for status in self.current_player.get_fleet_status():
            print(f"  {status}")

        print(f"\n{self.other_player.name}'s Fleet Status:")
        for status in self.other_player.get_fleet_status():
            ship_name = status.split(' (')[0]
            if 'sunk' in status:
                print(f"  {ship_name} - SUNK")
            else:
                print(f"  {ship_name} - Still afloat")

        # Get attack coordinates
        print(f"\n{self.current_player.name}, choose your target!")
        row, col = self.get_coordinate_input("Enter attack coordinates (e.g., A5): ")

        # Execute attack
        hit_result, ship_sunk, ship_name = self.other_player.receive_attack(row, col)

        # Display attack result
        self.display_attack_result(row, col, hit_result, ship_sunk, ship_name)

        input("Press Enter to end your turn...")

    def display_attack_result(self, row, col, hit_result, ship_sunk, ship_name):
        """
        Display the result of an attack.

        Args:
            row (int): Attack row
            col (int): Attack column
            hit_result (str): Result of the attack
            ship_sunk (bool): Whether a ship was sunk
            ship_name (str): Name of ship hit/sunk
        """
        coord_str = f"{chr(ord('A') + row)}{col}"

        print(f"\nAttack on {coord_str}:")

        if hit_result == 'hit':
            print("🎯 HIT!")
            if ship_sunk:
                print(f"💥 You sunk the {ship_name}!")
            else:
                print(f"⚡ You hit the {ship_name}!")
        elif hit_result == 'miss':
            print("💧 Miss!")
        elif hit_result == 'already_attacked':
            print("❌ You already attacked that position!")
        elif hit_result == 'invalid':
            print("❌ Invalid coordinates!")

    def display_game_over(self):
        """Display game over screen."""
        self.clear_screen()

        print("\n" + "="*50)
        print("                GAME OVER")
        print("="*50)

        if self.winner:
            print(f"\n🏆 {self.winner.name} WINS! 🏆")
            print(f"\n{self.winner.name} has sunk all of {self.other_player.name}'s ships!")

        # Show final board states
        print(f"\nFinal Board - {self.player1.name}:")
        self.display_board(self.player1.get_board_display(), f"{self.player1.name}'s Fleet")

        print(f"\nFinal Board - {self.player2.name}:")
        self.display_board(self.player2.get_board_display(), f"{self.player2.name}'s Fleet")

        print("\nThanks for playing Battleships!")

    def play(self):
        """Main game loop."""
        try:
            self.setup_players()
            self.ship_placement_phase()
            self.battle_phase()
        except KeyboardInterrupt:
            print("\n\nGame interrupted. Thanks for playing!")
        except Exception as e:
            print(f"\nAn error occurred: {e}")
            print("Game ended unexpectedly.")


def main():
    """Entry point for the game."""
    game = BattleshipsGame()
    game.play()


if __name__ == "__main__":
    main()