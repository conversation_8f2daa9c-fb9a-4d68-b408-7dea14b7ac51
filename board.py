"""
Board class for Battleships game.
Manages the 10x10 game grid, ship placement, and attack tracking.
"""

from ship import Ship


class Board:
    """Represents a game board for Battleships."""

    def __init__(self):
        """Initialize an empty 10x10 board."""
        self.size = 10
        self.grid = [['~' for _ in range(self.size)] for _ in range(self.size)]
        self.ships = []
        self.attacks = set()  # Set of (row, col) tuples that have been attacked
        self.hits = set()     # Set of (row, col) tuples that were hits
        self.misses = set()   # Set of (row, col) tuples that were misses

    def is_valid_position(self, row, col):
        """
        Check if a position is valid on the board.

        Args:
            row (int): Row coordinate (0-9)
            col (int): Column coordinate (0-9)

        Returns:
            bool: True if position is valid
        """
        return 0 <= row < self.size and 0 <= col < self.size

    def can_place_ship(self, ship, start_row, start_col, orientation):
        """
        Check if a ship can be placed at the given position.

        Args:
            ship (Ship): Ship to place
            start_row (int): Starting row
            start_col (int): Starting column
            orientation (str): 'horizontal' or 'vertical'

        Returns:
            bool: True if ship can be placed
        """
        # Calculate positions the ship would occupy
        positions = []
        if orientation == 'horizontal':
            if start_col + ship.size > self.size:
                return False
            positions = [(start_row, start_col + i) for i in range(ship.size)]
        elif orientation == 'vertical':
            if start_row + ship.size > self.size:
                return False
            positions = [(start_row + i, start_col) for i in range(ship.size)]
        else:
            return False

        # Check if all positions are valid and empty
        for row, col in positions:
            if not self.is_valid_position(row, col):
                return False
            if self.grid[row][col] != '~':
                return False

        # Check for adjacent ships (ships can't touch)
        for row, col in positions:
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0:
                        continue
                    adj_row, adj_col = row + dr, col + dc
                    if (self.is_valid_position(adj_row, adj_col) and
                        self.grid[adj_row][adj_col] == 'S'):
                        return False

        return True

    def place_ship(self, ship, start_row, start_col, orientation):
        """
        Place a ship on the board.

        Args:
            ship (Ship): Ship to place
            start_row (int): Starting row
            start_col (int): Starting column
            orientation (str): 'horizontal' or 'vertical'

        Returns:
            bool: True if ship was successfully placed
        """
        if not self.can_place_ship(ship, start_row, start_col, orientation):
            return False

        # Place the ship
        if ship.place(start_row, start_col, orientation):
            self.ships.append(ship)
            # Mark ship positions on grid
            for row, col in ship.get_positions():
                self.grid[row][col] = 'S'
            return True
        return False

    def attack(self, row, col):
        """
        Attack a position on the board.

        Args:
            row (int): Row to attack
            col (int): Column to attack

        Returns:
            tuple: (hit_result, ship_sunk, ship_name)
                hit_result: 'hit', 'miss', or 'already_attacked'
                ship_sunk: True if a ship was sunk by this attack
                ship_name: Name of ship that was hit/sunk, or None
        """
        if not self.is_valid_position(row, col):
            return 'invalid', False, None

        if (row, col) in self.attacks:
            return 'already_attacked', False, None

        self.attacks.add((row, col))

        # Check if any ship was hit
        for ship in self.ships:
            if ship.hit(row, col):
                self.hits.add((row, col))
                self.grid[row][col] = 'X'  # Mark as hit
                ship_sunk = ship.is_sunk()
                return 'hit', ship_sunk, ship.name

        # No ship was hit
        self.misses.add((row, col))
        self.grid[row][col] = 'O'  # Mark as miss
        return 'miss', False, None

    def all_ships_sunk(self):
        """
        Check if all ships on the board have been sunk.

        Returns:
            bool: True if all ships are sunk
        """
        return all(ship.is_sunk() for ship in self.ships)

    def get_display_grid(self, show_ships=True):
        """
        Get a copy of the grid for display purposes.

        Args:
            show_ships (bool): Whether to show ship positions

        Returns:
            list: 2D list representing the board state
        """
        display_grid = []
        for row in range(self.size):
            display_row = []
            for col in range(self.size):
                cell = self.grid[row][col]
                if not show_ships and cell == 'S':
                    cell = '~'  # Hide ships from opponent's view
                display_row.append(cell)
            display_grid.append(display_row)
        return display_grid

    def get_ship_status(self):
        """
        Get status of all ships on the board.

        Returns:
            list: List of ship status strings
        """
        return [str(ship) for ship in self.ships]