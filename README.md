# Battleships Game

A classic Battleships game implementation in Python for 1v1 local play, based on Has<PERSON>'s Battleships rules.

## Features

- **Classic Hasbro Battleships Rules**: Standard fleet composition and gameplay
- **Local 1v1 Play**: Two players take turns on the same computer
- **Interactive Ship Placement**: Place your fleet strategically on the board
- **Turn-based Combat**: Attack your opponent's fleet and track hits/misses
- **Visual Board Display**: Clear console-based interface with coordinate system
- **Win Condition**: Sink all enemy ships to win

## Fleet Composition

Each player has a fleet of 5 ships:
- **Carrier** (5 squares)
- **Battleship** (4 squares)
- **Cruiser** (3 squares)
- **Submarine** (3 squares)
- **Destroyer** (2 squares)

## How to Play

### Prerequisites
- Python 3.6 or higher

### Running the Game

You can play the game in two ways:

#### Graphical Interface (Recommended)
1. **Start the GUI game:**
   ```bash
   python battleships_gui.py
   ```
   or
   ```bash
   python gui_game.py
   ```

#### Console Interface
1. **Start the console game:**
   ```bash
   python game.py
   ```

2. **Player Setup:**
   - Enter names for Player 1 and Player 2 in the dialog boxes
   - Players will take turns placing their ships

3. **Ship Placement Phase:**
   - **GUI**: Click on ship buttons to select, choose orientation, then click on your board to place
   - **Console**: Choose ship, starting position (e.g., A5), and orientation (H/V)
   - Ships cannot overlap or touch each other
   - The other player should look away during placement

4. **Battle Phase:**
   - **GUI**: Click on your opponent's board to attack
   - **Console**: Enter coordinates to attack (e.g., A5)
   - Track hits (💥), misses (💧), and sunk ships
   - First player to sink all enemy ships wins!

### Controls

- **Coordinates**: Use format like "A5" (row A-J, column 0-9)
- **Orientation**: H for horizontal, V for vertical
- **Ship Selection**: Use numbers 0-4 to select ships during placement

### Board Symbols

- `~` = Water (empty)
- `S` = Ship (your ships only)
- `X` = Hit
- `O` = Miss

## Game Rules

1. **Ship Placement:**
   - Ships must be placed entirely within the 10x10 grid
   - Ships cannot overlap
   - Ships cannot be adjacent (must have at least one empty space between them)
   - Ships can be placed horizontally or vertically only

2. **Combat:**
   - Players alternate turns
   - One attack per turn
   - Must announce attack coordinates
   - Opponent announces "Hit" or "Miss"
   - When a ship is completely hit, announce "You sunk my [ship name]!"

3. **Winning:**
   - First player to sink all opponent's ships wins
   - Game displays final board states

## File Structure

- `battleships_gui.py` - **Main GUI launcher (recommended)**
- `gui_game.py` - Graphical user interface implementation
- `game.py` - Console-based game controller and user interface
- `player.py` - Player class managing individual players
- `board.py` - Board class managing the game grid
- `ship.py` - Ship class and fleet definitions
- `test_game.py` - Test suite for game components
- `README.md` - This file

## Testing

Run the test suite to verify all components work correctly:

```bash
python test_game.py
```

## Development

The game is built with a modular architecture:

- **Ship**: Handles individual ship properties and state
- **Board**: Manages the 10x10 grid, ship placement, and attacks
- **Player**: Combines board and fleet for each player
- **Game**: Controls overall game flow and user interface

## Troubleshooting

- **Python not found**: Make sure Python is installed and in your PATH
- **Import errors**: Ensure all files are in the same directory
- **Display issues**: Game works best in a terminal with at least 80 character width

## Future Enhancements

Potential improvements for future versions:
- AI opponent
- Network multiplayer
- Graphical user interface
- Save/load game state
- Different difficulty levels
- Sound effects

---

Enjoy playing Battleships! 🚢⚓