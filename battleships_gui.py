#!/usr/bin/env python3
"""
Battleships GUI Launcher
Simple launcher script for the graphical Battleships game.
"""

import sys
import tkinter as tk
from tkinter import messagebox

def check_requirements():
    """Check if all required modules are available."""
    try:
        import tkinter
        from player import Player
        from ship import create_fleet
        from board import Board
        return True
    except ImportError as e:
        messagebox.showerror("Missing Requirements", f"Required module not found: {e}")
        return False

def main():
    """Main launcher function."""
    if not check_requirements():
        sys.exit(1)

    try:
        from gui_game import BattleshipsGUI
        app = BattleshipsGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("Error", f"Failed to start game: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()