"""
Player class for Battleships game.
Manages a player's board and fleet.
"""

from board import Board
from ship import create_fleet


class Player:
    """Represents a player in the Battleships game."""

    def __init__(self, name):
        """
        Initialize a player.

        Args:
            name (str): Player's name
        """
        self.name = name
        self.board = Board()
        self.fleet = create_fleet()
        self.ships_placed = 0

    def get_unplaced_ships(self):
        """
        Get list of ships that haven't been placed yet.

        Returns:
            list: List of unplaced Ship objects
        """
        return [ship for ship in self.fleet if not ship.is_placed]

    def place_ship(self, ship_index, start_row, start_col, orientation):
        """
        Place a ship on the player's board.

        Args:
            ship_index (int): Index of ship in fleet to place
            start_row (int): Starting row
            start_col (int): Starting column
            orientation (str): 'horizontal' or 'vertical'

        Returns:
            bool: True if ship was successfully placed
        """
        if ship_index < 0 or ship_index >= len(self.fleet):
            return False

        ship = self.fleet[ship_index]
        if ship.is_placed:
            return False

        if self.board.place_ship(ship, start_row, start_col, orientation):
            self.ships_placed += 1
            return True
        return False

    def all_ships_placed(self):
        """
        Check if all ships have been placed.

        Returns:
            bool: True if all ships are placed
        """
        return self.ships_placed == len(self.fleet)

    def receive_attack(self, row, col):
        """
        Receive an attack on this player's board.

        Args:
            row (int): Row being attacked
            col (int): Column being attacked

        Returns:
            tuple: (hit_result, ship_sunk, ship_name)
        """
        return self.board.attack(row, col)

    def has_lost(self):
        """
        Check if this player has lost (all ships sunk).

        Returns:
            bool: True if all ships are sunk
        """
        return self.board.all_ships_sunk()

    def get_board_display(self, show_ships=True):
        """
        Get the player's board for display.

        Args:
            show_ships (bool): Whether to show ship positions

        Returns:
            list: 2D list representing the board
        """
        return self.board.get_display_grid(show_ships)

    def get_fleet_status(self):
        """
        Get status of all ships in the fleet.

        Returns:
            list: List of ship status strings
        """
        return self.board.get_ship_status()