"""
Player class for Battleships game.
Manages a player's board and fleet.
"""

from board import Board
from ship import create_fleet


class Player:
    """Represents a player in the Battleships game."""

    def __init__(self, name):
        """
        Initialize a player.

        Args:
            name (str): Player's name
        """
        self.name = name
        self.board = Board()
        self.fleet = create_fleet()
        self.ships_placed = 0

    def get_unplaced_ships(self):
        """
        Get list of ships that haven't been placed yet.

        Returns:
            list: List of unplaced Ship objects
        """
        return [ship for ship in self.fleet if not ship.is_placed]

    def place_ship(self, ship_index, start_row, start_col, orientation):
        """
        Place a ship on the player's board.

        Args:
            ship_index (int): Index of ship in fleet to place
            start_row (int): Starting row
            start_col (int): Starting column
            orientation (str): 'horizontal' or 'vertical'

        Returns:
            bool: True if ship was successfully placed
        """
        if ship_index < 0 or ship_index >= len(self.fleet):
            return False

        ship = self.fleet[ship_index]
        if ship.is_placed:
            return False

        if self.board.place_ship(ship, start_row, start_col, orientation):
            self.ships_placed += 1
            return True
        return False

    def all_ships_placed(self):
        """
        Check if all ships have been placed.

        Returns:
            bool: True if all ships are placed
        """
        return self.ships_placed == len(self.fleet)

    def receive_attack(self, row, col):
        """
        Receive an attack on this player's board.

        Args:
            row (int): Row being attacked
            col (int): Column being attacked

        Returns:
            tuple: (hit_result, ship_sunk, ship_name)
        """
        return self.board.attack(row, col)

    def has_lost(self):
        """
        Check if this player has lost (all ships sunk).

        Returns:
            bool: True if all ships are sunk
        """
        return self.board.all_ships_sunk()

    def get_board_display(self, show_ships=True):
        """
        Get the player's board for display.

        Args:
            show_ships (bool): Whether to show ship positions

        Returns:
            list: 2D list representing the board
        """
        return self.board.get_display_grid(show_ships)

    def get_fleet_status(self):
        """
        Get status of all ships in the fleet.

        Returns:
            list: List of ship status strings
        """
        return self.board.get_ship_status()

    def get_ship_at_position(self, row, col):
        """
        Get the ship at a specific position.

        Args:
            row (int): Row coordinate
            col (int): Column coordinate

        Returns:
            Ship or None: Ship at position, or None if no ship
        """
        return self.board.get_ship_at_position(row, col)

    def remove_ship(self, ship):
        """
        Remove a ship from the board.

        Args:
            ship (Ship): Ship to remove

        Returns:
            bool: True if ship was successfully removed
        """
        if ship in self.fleet and ship.is_placed:
            if self.board.remove_ship(ship):
                self.ships_placed -= 1
                return True
        return False

    def rotate_ship(self, ship):
        """
        Rotate a placed ship (change its orientation).

        Args:
            ship (Ship): Ship to rotate

        Returns:
            bool: True if ship was successfully rotated
        """
        if not ship.is_placed or ship not in self.fleet:
            return False

        # Get current ship position and orientation
        positions = ship.get_positions()
        if not positions:
            return False

        start_row, start_col = positions[0]

        # Determine current orientation
        if len(positions) == 1:
            # Single cell ship, rotation doesn't matter
            return True

        current_orientation = 'horizontal' if positions[0][0] == positions[1][0] else 'vertical'
        new_orientation = 'vertical' if current_orientation == 'horizontal' else 'horizontal'

        # Try to rotate the ship
        if self.board.can_place_ship_excluding_self(ship, start_row, start_col, new_orientation):
            # Remove ship temporarily
            self.board.remove_ship(ship)
            self.ships_placed -= 1

            # Place with new orientation
            if self.board.place_ship(ship, start_row, start_col, new_orientation):
                self.ships_placed += 1
                return True
            else:
                # If placement fails, restore original placement
                self.board.place_ship(ship, start_row, start_col, current_orientation)
                self.ships_placed += 1
                return False

        return False