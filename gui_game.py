"""
GUI Battleships Game using tkinter.
Modern graphical interface for the classic Battleships game.
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import tkinter.font as tkFont
from player import Player
from ship import create_fleet
import random


class BattleshipsGUI:
    """Main GUI class for Battleships game."""

    def __init__(self):
        """Initialize the GUI application."""
        self.root = tk.Tk()
        self.root.title("Battleships Game")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2c3e50')

        # Game state
        self.player1 = None
        self.player2 = None
        self.current_player = None
        self.other_player = None
        self.game_state = "setup"  # setup, placement, battle, game_over
        self.selected_ship = None
        self.ship_orientation = "horizontal"

        # GUI components
        self.main_frame = None
        self.player1_board_frame = None
        self.player2_board_frame = None
        self.control_frame = None
        self.status_label = None
        self.player1_buttons = []
        self.player2_buttons = []

        # Colors
        self.colors = {
            'water': '#3498db',
            'ship': '#34495e',
            'ship_rotatable': '#2980b9',  # Slightly different blue for rotatable ships
            'hit': '#e74c3c',
            'miss': '#95a5a6',
            'selected': '#f39c12',
            'hover': '#5dade2'
        }

        self.setup_gui()

    def setup_gui(self):
        """Set up the main GUI layout."""
        # Main title
        title_label = tk.Label(
            self.root,
            text="⚓ BATTLESHIPS ⚓",
            font=('Arial', 24, 'bold'),
            fg='white',
            bg='#2c3e50'
        )
        title_label.pack(pady=10)

        # Main container
        self.main_frame = tk.Frame(self.root, bg='#2c3e50')
        self.main_frame.pack(expand=True, fill='both', padx=20, pady=10)

        # Status label
        self.status_label = tk.Label(
            self.main_frame,
            text="Welcome to Battleships! Click 'New Game' to start.",
            font=('Arial', 14),
            fg='white',
            bg='#2c3e50'
        )
        self.status_label.pack(pady=10)

        # Control frame
        self.control_frame = tk.Frame(self.main_frame, bg='#2c3e50')
        self.control_frame.pack(pady=10)

        # New game button
        new_game_btn = tk.Button(
            self.control_frame,
            text="New Game",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            padx=20,
            pady=10,
            command=self.start_new_game
        )
        new_game_btn.pack(side='left', padx=10)

        # Quit button
        quit_btn = tk.Button(
            self.control_frame,
            text="Quit",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=10,
            command=self.root.quit
        )
        quit_btn.pack(side='right', padx=10)

    def start_new_game(self):
        """Start a new game."""
        # Get player names
        player1_name = simpledialog.askstring("Player 1", "Enter name for Player 1:", initialvalue="Player 1")
        if not player1_name:
            return

        player2_name = simpledialog.askstring("Player 2", "Enter name for Player 2:", initialvalue="Player 2")
        if not player2_name:
            return

        # Initialize players
        self.player1 = Player(player1_name)
        self.player2 = Player(player2_name)
        self.current_player = self.player1
        self.other_player = self.player2

        # Set game state
        self.game_state = "placement"

        # Create game boards
        self.create_game_boards()

        # Start ship placement
        self.start_ship_placement()

    def create_game_boards(self):
        """Create the visual game boards."""
        # Clear existing boards
        for widget in self.main_frame.winfo_children():
            if widget != self.status_label and widget != self.control_frame:
                widget.destroy()

        # Boards container
        boards_frame = tk.Frame(self.main_frame, bg='#2c3e50')
        boards_frame.pack(expand=True, fill='both')

        # Player 1 board
        self.player1_board_frame = tk.LabelFrame(
            boards_frame,
            text=f"{self.player1.name}'s Fleet",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg='#34495e',
            bd=2,
            relief='raised'
        )
        self.player1_board_frame.pack(side='left', expand=True, fill='both', padx=10, pady=10)

        # Player 2 board
        self.player2_board_frame = tk.LabelFrame(
            boards_frame,
            text=f"{self.player2.name}'s Fleet",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg='#34495e',
            bd=2,
            relief='raised'
        )
        self.player2_board_frame.pack(side='right', expand=True, fill='both', padx=10, pady=10)

        # Create board grids
        self.create_board_grid(self.player1_board_frame, 'player1')
        self.create_board_grid(self.player2_board_frame, 'player2')

    def create_board_grid(self, parent_frame, player_id):
        """Create a 10x10 grid of buttons for a player's board."""
        # Grid frame
        grid_frame = tk.Frame(parent_frame, bg='#34495e')
        grid_frame.pack(expand=True, fill='both', padx=10, pady=10)

        # Column headers (0-9)
        tk.Label(grid_frame, text="", bg='#34495e', width=3).grid(row=0, column=0)
        for col in range(10):
            tk.Label(
                grid_frame,
                text=str(col),
                font=('Arial', 10, 'bold'),
                fg='white',
                bg='#34495e',
                width=3
            ).grid(row=0, column=col+1)

        # Create button grid
        buttons = []
        for row in range(10):
            # Row header (A-J)
            tk.Label(
                grid_frame,
                text=chr(ord('A') + row),
                font=('Arial', 10, 'bold'),
                fg='white',
                bg='#34495e',
                width=3
            ).grid(row=row+1, column=0)

            button_row = []
            for col in range(10):
                btn = tk.Button(
                    grid_frame,
                    text="",
                    width=3,
                    height=1,
                    bg=self.colors['water'],
                    relief='raised',
                    bd=2,
                    command=lambda r=row, c=col, p=player_id: self.cell_clicked(r, c, p)
                )

                # Add hover effects
                btn.bind("<Enter>", lambda e, b=btn: self.on_cell_hover(e, b))
                btn.bind("<Leave>", lambda e, b=btn: self.on_cell_leave(e, b))
                btn.grid(row=row+1, column=col+1, padx=1, pady=1)
                button_row.append(btn)
            buttons.append(button_row)

        # Store button references
        if player_id == 'player1':
            self.player1_buttons = buttons
        else:
            self.player2_buttons = buttons

    def cell_clicked(self, row, col, player_id):
        """Handle cell click events."""
        if self.game_state == "placement":
            self.handle_placement_click(row, col, player_id)
        elif self.game_state == "battle":
            self.handle_attack_click(row, col, player_id)

    def start_ship_placement(self):
        """Start the ship placement phase."""
        self.update_status(f"{self.current_player.name}, place your ships! Select a ship and click on the board. Click on placed ships to rotate them.")
        self.create_ship_selection_panel()

    def create_ship_selection_panel(self):
        """Create the ship selection panel for placement."""
        # Clear existing control frame
        for widget in self.control_frame.winfo_children():
            widget.destroy()

        # Ship selection frame
        ship_frame = tk.LabelFrame(
            self.control_frame,
            text="Select Ship to Place (Click placed ships to rotate)",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='#34495e',
            bd=2
        )
        ship_frame.pack(side='left', padx=10, pady=5)

        # Get unplaced ships
        unplaced_ships = self.current_player.get_unplaced_ships()

        for i, ship in enumerate(unplaced_ships):
            btn = tk.Button(
                ship_frame,
                text=f"{ship.name} ({ship.size})",
                font=('Arial', 10),
                bg='#3498db',
                fg='white',
                padx=10,
                pady=5,
                command=lambda s=ship: self.select_ship(s)
            )
            btn.pack(side='left', padx=5, pady=5)

        # Orientation controls
        orient_frame = tk.LabelFrame(
            self.control_frame,
            text="Orientation",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='#34495e',
            bd=2
        )
        orient_frame.pack(side='left', padx=10, pady=5)

        tk.Button(
            orient_frame,
            text="Horizontal",
            font=('Arial', 10),
            bg='#27ae60' if self.ship_orientation == 'horizontal' else '#95a5a6',
            fg='white',
            padx=10,
            pady=5,
            command=lambda: self.set_orientation('horizontal')
        ).pack(side='left', padx=5, pady=5)

        tk.Button(
            orient_frame,
            text="Vertical",
            font=('Arial', 10),
            bg='#27ae60' if self.ship_orientation == 'vertical' else '#95a5a6',
            fg='white',
            padx=10,
            pady=5,
            command=lambda: self.set_orientation('vertical')
        ).pack(side='left', padx=5, pady=5)

        # Next player / Start battle button
        if self.current_player.all_ships_placed():
            if self.other_player.all_ships_placed():
                tk.Button(
                    self.control_frame,
                    text="Start Battle!",
                    font=('Arial', 12, 'bold'),
                    bg='#e74c3c',
                    fg='white',
                    padx=20,
                    pady=10,
                    command=self.start_battle
                ).pack(side='right', padx=10, pady=5)
            else:
                tk.Button(
                    self.control_frame,
                    text="Next Player",
                    font=('Arial', 12, 'bold'),
                    bg='#f39c12',
                    fg='white',
                    padx=20,
                    pady=10,
                    command=self.switch_to_next_player
                ).pack(side='right', padx=10, pady=5)

    def select_ship(self, ship):
        """Select a ship for placement."""
        self.selected_ship = ship
        self.update_status(f"Selected {ship.name} ({ship.size} squares). Choose orientation and click on the board.")

    def set_orientation(self, orientation):
        """Set ship orientation."""
        self.ship_orientation = orientation
        self.create_ship_selection_panel()  # Refresh to update button colors

    def handle_placement_click(self, row, col, player_id):
        """Handle click during ship placement phase."""
        # Only allow placement on current player's board
        current_player_id = 'player1' if self.current_player == self.player1 else 'player2'
        if player_id != current_player_id:
            return

        # Check if there's already a ship at this position (for rotation)
        existing_ship = self.current_player.get_ship_at_position(row, col)
        if existing_ship:
            # Rotate the existing ship
            if self.current_player.rotate_ship(existing_ship):
                self.update_board_display()
                self.update_status(f"Rotated {existing_ship.name}! Click on ships to rotate them or select a new ship to place.")
            else:
                messagebox.showwarning("Cannot Rotate", f"Cannot rotate {existing_ship.name} - not enough space!")
            return

        if not self.selected_ship:
            messagebox.showwarning("No Ship Selected", "Please select a ship to place first, or click on a placed ship to rotate it!")
            return

        # Try to place the ship
        ship_index = self.current_player.fleet.index(self.selected_ship)
        if self.current_player.place_ship(ship_index, row, col, self.ship_orientation):
            self.update_board_display()
            self.selected_ship = None

            # Check if all ships are placed
            if self.current_player.all_ships_placed():
                if self.other_player.all_ships_placed():
                    self.update_status("All ships placed! Ready to start battle.")
                else:
                    self.update_status(f"{self.current_player.name} finished placing ships!")
            else:
                self.update_status(f"Ship placed! Select next ship to place, or click on placed ships to rotate them.")

            self.create_ship_selection_panel()
        else:
            messagebox.showerror("Invalid Placement", "Cannot place ship there! Check for overlaps or boundaries.")

    def switch_to_next_player(self):
        """Switch to the next player for ship placement."""
        self.current_player, self.other_player = self.other_player, self.current_player

        # Hide current boards and show message
        messagebox.showinfo("Player Switch", f"{self.current_player.name}'s turn to place ships!\n\nMake sure {self.other_player.name} is not looking!")

        # Update board labels
        self.player1_board_frame.config(text=f"{self.player1.name}'s Fleet")
        self.player2_board_frame.config(text=f"{self.player2.name}'s Fleet")

        # Update display
        self.update_board_display()
        self.create_ship_selection_panel()
        self.update_status(f"{self.current_player.name}, place your ships! Click on placed ships to rotate them.")

    def start_battle(self):
        """Start the battle phase."""
        self.game_state = "battle"
        self.current_player = self.player1
        self.other_player = self.player2

        # Clear control frame
        for widget in self.control_frame.winfo_children():
            widget.destroy()

        # Create battle controls
        self.create_battle_controls()

        # Update board display for battle
        self.update_board_display()
        self.update_status(f"{self.current_player.name}'s turn! Click on {self.other_player.name}'s board to attack.")

    def create_battle_controls(self):
        """Create controls for the battle phase."""
        # Current player indicator
        player_frame = tk.LabelFrame(
            self.control_frame,
            text="Current Turn",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='#34495e',
            bd=2
        )
        player_frame.pack(side='left', padx=10, pady=5)

        self.current_player_label = tk.Label(
            player_frame,
            text=self.current_player.name,
            font=('Arial', 14, 'bold'),
            fg='#f39c12',
            bg='#34495e'
        )
        self.current_player_label.pack(padx=10, pady=5)

        # Fleet status
        status_frame = tk.LabelFrame(
            self.control_frame,
            text="Fleet Status",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='#34495e',
            bd=2
        )
        status_frame.pack(side='left', padx=10, pady=5)

        self.fleet_status_label = tk.Label(
            status_frame,
            text="",
            font=('Arial', 10),
            fg='white',
            bg='#34495e',
            justify='left'
        )
        self.fleet_status_label.pack(padx=10, pady=5)

        self.update_fleet_status()

    def handle_attack_click(self, row, col, player_id):
        """Handle click during battle phase."""
        # Only allow attacks on opponent's board
        target_player_id = 'player2' if self.current_player == self.player1 else 'player1'
        if player_id != target_player_id:
            messagebox.showwarning("Invalid Target", "You can only attack your opponent's board!")
            return

        # Execute attack
        hit_result, ship_sunk, ship_name = self.other_player.receive_attack(row, col)

        if hit_result == 'already_attacked':
            messagebox.showwarning("Already Attacked", "You already attacked that position!")
            return
        elif hit_result == 'invalid':
            messagebox.showerror("Invalid Attack", "Invalid coordinates!")
            return

        # Add visual feedback
        target_player_id = 'player2' if self.current_player == self.player1 else 'player1'
        if hit_result == 'hit':
            self.add_visual_feedback(row, col, target_player_id, 'hit')
        elif hit_result == 'miss':
            self.add_visual_feedback(row, col, target_player_id, 'miss')

        # Update display after a short delay
        self.root.after(300, self.update_board_display)

        # Show attack result
        self.root.after(400, lambda: self.show_attack_result(row, col, hit_result, ship_sunk, ship_name))

        # Check for game over
        if self.other_player.has_lost():
            self.game_over()
            return

        # Switch turns
        self.current_player, self.other_player = self.other_player, self.current_player
        self.current_player_label.config(text=self.current_player.name)
        self.update_fleet_status()
        self.update_status(f"{self.current_player.name}'s turn! Click on {self.other_player.name}'s board to attack.")

    def show_attack_result(self, row, col, hit_result, ship_sunk, ship_name):
        """Show the result of an attack."""
        coord_str = f"{chr(ord('A') + row)}{col}"

        if hit_result == 'hit':
            if ship_sunk:
                messagebox.showinfo("Direct Hit!", f"🎯 HIT at {coord_str}!\n💥 You sunk the {ship_name}!")
            else:
                messagebox.showinfo("Direct Hit!", f"🎯 HIT at {coord_str}!\n⚡ You hit the {ship_name}!")
        elif hit_result == 'miss':
            messagebox.showinfo("Miss", f"💧 Miss at {coord_str}!")

    def update_board_display(self):
        """Update the visual display of both boards."""
        # Update player 1 board
        board1 = self.player1.get_board_display(show_ships=True)
        for row in range(10):
            for col in range(10):
                cell = board1[row][col]
                btn = self.player1_buttons[row][col]

                if cell == '~':
                    btn.config(bg=self.colors['water'], text="")
                elif cell == 'S':
                    # During placement, show ships as rotatable if it's current player's turn
                    if self.game_state == "placement" and self.current_player == self.player1:
                        btn.config(bg=self.colors['ship_rotatable'], text="🚢")
                    else:
                        btn.config(bg=self.colors['ship'], text="🚢")
                elif cell == 'X':
                    btn.config(bg=self.colors['hit'], text="💥")
                elif cell == 'O':
                    btn.config(bg=self.colors['miss'], text="💧")

        # Update player 2 board
        board2 = self.player2.get_board_display(show_ships=(self.game_state == "placement" or self.game_state == "game_over"))
        for row in range(10):
            for col in range(10):
                cell = board2[row][col]
                btn = self.player2_buttons[row][col]

                if cell == '~':
                    btn.config(bg=self.colors['water'], text="")
                elif cell == 'S':
                    # During placement, show ships as rotatable if it's current player's turn
                    if self.game_state == "placement" and self.current_player == self.player2:
                        btn.config(bg=self.colors['ship_rotatable'], text="🚢")
                    else:
                        btn.config(bg=self.colors['ship'], text="🚢")
                elif cell == 'X':
                    btn.config(bg=self.colors['hit'], text="💥")
                elif cell == 'O':
                    btn.config(bg=self.colors['miss'], text="💧")

    def update_fleet_status(self):
        """Update the fleet status display."""
        if hasattr(self, 'fleet_status_label'):
            p1_ships = len([s for s in self.player1.fleet if not s.is_sunk()])
            p2_ships = len([s for s in self.player2.fleet if not s.is_sunk()])

            status_text = f"{self.player1.name}: {p1_ships} ships remaining\n{self.player2.name}: {p2_ships} ships remaining"
            self.fleet_status_label.config(text=status_text)

    def update_status(self, message):
        """Update the status message."""
        self.status_label.config(text=message)

    def game_over(self):
        """Handle game over."""
        self.game_state = "game_over"
        winner = self.current_player

        # Show all ships
        self.update_board_display()

        # Show game over message
        messagebox.showinfo("Game Over!", f"🏆 {winner.name} WINS! 🏆\n\nAll enemy ships have been sunk!")

        # Update status
        self.update_status(f"Game Over! {winner.name} is the winner!")

        # Clear control frame and add new game button
        for widget in self.control_frame.winfo_children():
            widget.destroy()

        tk.Button(
            self.control_frame,
            text="New Game",
            font=('Arial', 12, 'bold'),
            bg='#27ae60',
            fg='white',
            padx=20,
            pady=10,
            command=self.start_new_game
        ).pack(side='left', padx=10)

        tk.Button(
            self.control_frame,
            text="Quit",
            font=('Arial', 12, 'bold'),
            bg='#e74c3c',
            fg='white',
            padx=20,
            pady=10,
            command=self.root.quit
        ).pack(side='right', padx=10)

    def on_cell_hover(self, event, button):
        """Handle mouse hover over cell."""
        if self.game_state == "battle":
            current_bg = button.cget('bg')
            if current_bg == self.colors['water']:
                button.config(bg=self.colors['hover'])

    def on_cell_leave(self, event, button):
        """Handle mouse leave cell."""
        if self.game_state == "battle":
            current_bg = button.cget('bg')
            if current_bg == self.colors['hover']:
                button.config(bg=self.colors['water'])

    def add_visual_feedback(self, row, col, player_id, feedback_type):
        """Add visual feedback for actions."""
        buttons = self.player1_buttons if player_id == 'player1' else self.player2_buttons
        btn = buttons[row][col]

        original_bg = btn.cget('bg')

        if feedback_type == 'hit':
            # Flash red for hit
            btn.config(bg='#ff0000')
            self.root.after(200, lambda: btn.config(bg=self.colors['hit']))
        elif feedback_type == 'miss':
            # Flash gray for miss
            btn.config(bg='#808080')
            self.root.after(200, lambda: btn.config(bg=self.colors['miss']))

    def run(self):
        """Start the GUI application."""
        # Set window icon and additional properties
        try:
            self.root.iconname("Battleships")
            self.root.resizable(True, True)
            self.root.minsize(1000, 600)
        except:
            pass  # Ignore if icon setting fails

        # Center the window
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')

        self.root.mainloop()


def main():
    """Entry point for the GUI game."""
    app = BattleshipsGUI()
    app.run()


if __name__ == "__main__":
    main()