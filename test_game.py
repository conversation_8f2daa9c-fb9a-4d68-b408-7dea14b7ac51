#!/usr/bin/env python3
"""
Test script for Battleships game components.
"""

def test_ship():
    """Test Ship class functionality."""
    from ship import Ship, create_fleet

    print("Testing Ship class...")

    # Test ship creation
    ship = Ship("Destroyer", 2)
    assert ship.name == "Destroyer"
    assert ship.size == 2
    assert not ship.is_placed
    assert not ship.is_sunk()

    # Test ship placement
    assert ship.place(0, 0, 'horizontal')
    assert ship.is_placed
    assert ship.get_positions() == [(0, 0), (0, 1)]

    # Test hitting
    assert ship.hit(0, 0)
    assert not ship.is_sunk()
    assert ship.hit(0, 1)
    assert ship.is_sunk()

    # Test fleet creation
    fleet = create_fleet()
    assert len(fleet) == 5
    assert fleet[0].name == "Carrier"
    assert fleet[0].size == 5

    print("✓ Ship class tests passed!")


def test_board():
    """Test Board class functionality."""
    from board import Board
    from ship import Ship

    print("Testing Board class...")

    board = Board()
    ship = Ship("Test Ship", 3)

    # Test ship placement
    assert board.can_place_ship(ship, 0, 0, 'horizontal')
    assert board.place_ship(ship, 0, 0, 'horizontal')
    assert len(board.ships) == 1

    # Test attacks
    result, sunk, name = board.attack(0, 0)
    assert result == 'hit'
    assert not sunk
    assert name == "Test Ship"

    result, sunk, name = board.attack(0, 1)
    assert result == 'hit'

    result, sunk, name = board.attack(0, 2)
    assert result == 'hit'
    assert sunk
    assert name == "Test Ship"

    # Test miss
    result, sunk, name = board.attack(1, 0)
    assert result == 'miss'
    assert not sunk

    print("✓ Board class tests passed!")


def test_player():
    """Test Player class functionality."""
    from player import Player

    print("Testing Player class...")

    player = Player("Test Player")
    assert player.name == "Test Player"
    assert len(player.fleet) == 5
    assert not player.all_ships_placed()

    # Test ship placement
    unplaced = player.get_unplaced_ships()
    assert len(unplaced) == 5

    # Place a ship
    assert player.place_ship(0, 0, 0, 'horizontal')  # Carrier
    assert player.ships_placed == 1

    print("✓ Player class tests passed!")


def main():
    """Run all tests."""
    print("Running Battleships Game Tests")
    print("=" * 30)

    try:
        test_ship()
        test_board()
        test_player()

        print("\n🎉 All tests passed! The game should work correctly.")
        print("\nTo play the game, run: python game.py")

    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()